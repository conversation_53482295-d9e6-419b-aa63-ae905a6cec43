#!/usr/bin/env python3
"""
Interactive Scientific Calculator for Hexadecimal XOR Operations
This script provides an easy-to-use interface for generating outputs
from Data1 and Data2 inputs using the XOR formula.
"""

import sys
from test import ScientificCalculator, generate_output

def main():
    """Main interactive function"""
    calculator = ScientificCalculator()
    
    print("=" * 70)
    print("  SCIENTIFIC CALCULATION GENERATOR")
    print("  Formula: Output = Data1 XOR Data2 (Hexadecimal)")
    print("=" * 70)
    
    while True:
        print("\nOptions:")
        print("1. Calculate single pair (Data1, Data2)")
        print("2. Batch calculate from file")
        print("3. Show example calculations")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == '1':
            single_calculation(calculator)
        elif choice == '2':
            batch_calculation(calculator)
        elif choice == '3':
            show_examples(calculator)
        elif choice == '4':
            print("Thank you for using the Scientific Calculator!")
            break
        else:
            print("Invalid choice. Please enter 1, 2, 3, or 4.")

def single_calculation(calculator):
    """Handle single pair calculation"""
    print("\n" + "-" * 50)
    print("SINGLE CALCULATION")
    print("-" * 50)
    
    try:
        data1 = input("Enter Data1 (hexadecimal): ").strip()
        data2 = input("Enter Data2 (hexadecimal): ").strip()
        
        if not data1 or not data2:
            print("Error: Both Data1 and Data2 are required.")
            return
        
        if not calculator.validate_input(data1):
            print(f"Error: '{data1}' is not a valid hexadecimal number.")
            return
            
        if not calculator.validate_input(data2):
            print(f"Error: '{data2}' is not a valid hexadecimal number.")
            return
        
        result = calculator.calculate(data1, data2)
        
        print(f"\nRESULT:")
        print(f"Data1:  {data1.upper()}")
        print(f"Data2:  {data2.upper()}")
        print(f"Output: {result}")
        print(f"\nFormula: {data1.upper()} XOR {data2.upper()} = {result}")
        
    except KeyboardInterrupt:
        print("\nOperation cancelled.")
    except Exception as e:
        print(f"Error: {e}")

def batch_calculation(calculator):
    """Handle batch calculation from file or manual input"""
    print("\n" + "-" * 50)
    print("BATCH CALCULATION")
    print("-" * 50)
    
    print("Enter pairs of Data1,Data2 (one pair per line)")
    print("Format: Data1,Data2")
    print("Enter 'done' when finished, or 'cancel' to abort")
    print("Example: ABCD1234,5678EFGH")
    
    pairs = []
    line_num = 1
    
    while True:
        try:
            line = input(f"Pair {line_num}: ").strip()
            
            if line.lower() == 'done':
                break
            elif line.lower() == 'cancel':
                print("Batch calculation cancelled.")
                return
            
            if ',' not in line:
                print("Error: Please use format 'Data1,Data2'")
                continue
            
            parts = line.split(',')
            if len(parts) != 2:
                print("Error: Please provide exactly two values separated by comma")
                continue
            
            data1, data2 = parts[0].strip(), parts[1].strip()
            
            if not calculator.validate_input(data1) or not calculator.validate_input(data2):
                print(f"Error: Invalid hexadecimal values in '{line}'")
                continue
            
            pairs.append((data1, data2))
            line_num += 1
            
        except KeyboardInterrupt:
            print("\nBatch calculation cancelled.")
            return
    
    if not pairs:
        print("No valid pairs entered.")
        return
    
    print(f"\nProcessing {len(pairs)} pairs...")
    print("\nRESULTS:")
    print("-" * 60)
    
    results = calculator.batch_calculate(pairs)
    for i, result in enumerate(results, 1):
        print(f"{i:2d}. Data1: {result['data1']:>12} | Data2: {result['data2']:>12} | Output: {result['output']}")

def show_examples(calculator):
    """Show example calculations"""
    print("\n" + "-" * 50)
    print("EXAMPLE CALCULATIONS")
    print("-" * 50)
    
    examples = [
        ('8636936827A', '2359D848460E'),
        ('4009671D41D', 'A5CCA550BA00'),
        ('111111111111', '000000000000'),
        ('111111111111', '111111111111'),
        ('ABCDEF123456', '123456ABCDEF'),
        ('FFFFFFFFFF', '0000000001'),
    ]
    
    print("Formula: Output = Data1 XOR Data2")
    print("-" * 60)
    
    results = calculator.batch_calculate(examples)
    for i, result in enumerate(results, 1):
        print(f"{i}. Data1: {result['data1']:>12} | Data2: {result['data2']:>12} | Output: {result['output']}")
    
    print("\nXOR Logic Explanation:")
    print("- XOR returns 1 when bits are different, 0 when same")
    print("- 0 XOR 0 = 0, 1 XOR 1 = 0, 0 XOR 1 = 1, 1 XOR 0 = 1")
    print("- Example: A (1010) XOR 5 (0101) = F (1111)")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nProgram terminated by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
