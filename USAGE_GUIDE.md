# Usage Guide: Scientific Calculation Generator

## Quick Reference

**Formula**: `Output = Data1 XOR Data2`

**For any new Data1 and Data2 values, use one of these methods:**

### Method 1: Command Line (Fastest)
```bash
python calculator_cli.py <Data1> <Data2>
```

**Examples:**
```bash
python calculator_cli.py 8636936827A 2359D848460E
# Output: 2B3AB17EC474

python calculator_cli.py ABCD 1234  
# Output: 00000000B9F9

python calculator_cli.py FF00 00FF
# Output: 00000000FFFF
```

### Method 2: Python Code
```python
from test import generate_output

# For any new data pair:
result = generate_output('YOUR_DATA1', 'YOUR_DATA2')
print(result)
```

### Method 3: Interactive Mode
```bash
python calculator_interactive.py
```
Then select option 1 and enter your Data1 and Data2 values.

## Verified Test Cases

Based on the original data, here are the confirmed calculations:

| Data1        | Data2        | Output       | Verified |
|--------------|--------------|--------------|----------|
| 8636936827A  | 2359D848460E | 2B3AB17EC474 | ✓        |
| 4009671D41D  | A5CCA550BA00 | A1CC33216E1D | ✓        |
| 4007C0600DA  | A5CCA550BA00 | A1CCD956BADA | ✓        |
| 1111111111DA | A5CCA550BA00 | B4DDB441ABDA | ✓        |

## How XOR Works

XOR (Exclusive OR) compares each bit:
- Same bits (0,0 or 1,1) → 0
- Different bits (0,1 or 1,0) → 1

**Example breakdown:**
```
Data1: A (hex) = 1010 (binary)
Data2: 5 (hex) = 0101 (binary)
XOR:   F (hex) = 1111 (binary)
```

## Input Rules

✅ **Valid inputs:**
- `ABCD`, `abcd` (case insensitive)
- `123456789ABC`
- `FF00`
- `0` (single digit)

❌ **Invalid inputs:**
- `GHIJ` (contains non-hex characters)
- `0x1234` (don't include 0x prefix)
- Empty strings

## Output Format

- Always 12 characters
- Uppercase hexadecimal
- Zero-padded on the left if needed
- Examples: `000000000000`, `ABCDEF123456`

## For Client Use

**To solve any future data sets:**

1. **Single calculation:**
   ```bash
   python calculator_cli.py <YOUR_DATA1> <YOUR_DATA2>
   ```

2. **Multiple calculations:**
   Create a script like this:
   ```python
   from test import generate_output
   
   # Your data pairs
   data_pairs = [
       ('DATA1_VALUE1', 'DATA2_VALUE1'),
       ('DATA1_VALUE2', 'DATA2_VALUE2'),
       # Add more pairs as needed
   ]
   
   for data1, data2 in data_pairs:
       result = generate_output(data1, data2)
       print(f"{data1} XOR {data2} = {result}")
   ```

3. **Batch processing:**
   ```bash
   python calculator_interactive.py
   # Choose option 2 for batch calculation
   ```

## Troubleshooting

**Error: "Invalid hexadecimal input"**
- Check that your input only contains 0-9 and A-F
- Remove any '0x' prefix

**Error: "Command not found"**
- Make sure you're in the correct directory
- Use `python3` instead of `python` if needed

**Wrong output length**
- The generator automatically pads to 12 characters
- This matches the pattern from your original data

## Integration

To integrate this into other systems:

```python
# Import the function
from test import generate_output

# Use in your code
def process_data(data1, data2):
    return generate_output(data1, data2)

# Example usage
result = process_data('8636936827A', '2359D848460E')
assert result == '2B3AB17EC474'  # Verified correct
```

## Support

If you encounter any issues:
1. Check the input format (hexadecimal only)
2. Verify the data matches the examples
3. Run `python calculator_cli.py --examples` to see working examples
4. Use `python calculator_cli.py --help` for usage information
