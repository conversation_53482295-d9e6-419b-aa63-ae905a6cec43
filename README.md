# Scientific Calculation Generator

A number input-output generator for hexadecimal XOR operations, designed for scientific computing applications.

## Overview

This generator implements the formula: **Output = Data1 XOR Data2**

Where:
- Data1 and Data2 are hexadecimal numbers
- Output is the XOR result, formatted as a 12-character uppercase hexadecimal string

## Files

- `test.py` - Main calculator class with batch processing capabilities
- `calculator_interactive.py` - Interactive command-line interface
- `README.md` - This documentation file

## Quick Start

### Method 1: Using the Interactive Calculator

```bash
python calculator_interactive.py
```

This provides a menu-driven interface where you can:
1. Calculate single pairs
2. Process multiple pairs in batch
3. View example calculations
4. Get help and explanations

### Method 2: Using the Core Functions

```python
from test import generate_output, ScientificCalculator

# Simple calculation
result = generate_output('ABCD1234', '5678EFGH')
print(result)  # Output: FB95DD5C

# Using the class directly
calculator = ScientificCalculator()
result = calculator.calculate('8636936827A', '2359D848460E')
print(result)  # Output: 2B3AB17EC474
```

### Method 3: Running Test Examples

```bash
python test.py
```

This runs all the test examples and shows the formatted output.

## Formula Explanation

The XOR (Exclusive OR) operation works on binary representations:
- 0 XOR 0 = 0
- 1 XOR 1 = 0  
- 0 XOR 1 = 1
- 1 XOR 0 = 1

For hexadecimal numbers, each digit is converted to 4-bit binary, XOR is applied bit by bit, then converted back to hexadecimal.

### Example:
```
Data1: A (1010 in binary)
Data2: 5 (0101 in binary)
XOR:   F (1111 in binary)
```

## Input Format

- **Hexadecimal strings**: Can contain digits 0-9 and letters A-F (case insensitive)
- **No prefix required**: Don't include '0x' prefix
- **Variable length**: Shorter numbers are automatically padded with leading zeros
- **Examples**: `ABCD`, `1234567890AB`, `FF00`, `0`

## Output Format

- **12-character uppercase hexadecimal string**
- **Zero-padded**: Shorter results are padded with leading zeros
- **Examples**: `000000000000`, `ABCDEF123456`, `FFFFFFFFFF00`

## API Reference

### ScientificCalculator Class

```python
calculator = ScientificCalculator(output_length=12)
```

#### Methods:

- `calculate(data1, data2)` - Calculate XOR of two hex numbers
- `batch_calculate(data_pairs)` - Process list of (data1, data2) tuples
- `validate_input(hex_string)` - Check if input is valid hexadecimal
- `hex_xor(hex1, hex2)` - Core XOR implementation

### Convenience Functions

- `generate_output(data1, data2)` - Simple function for single calculations

## Example Calculations

| Data1        | Data2        | Output       |
|--------------|--------------|--------------|
| 8636936827A  | 2359D848460E | 2B3AB17EC474 |
| 4009671D41D  | A5CCA550BA00 | A1CC33216E1D |
| 111111111111 | 000000000000 | 111111111111 |
| 111111111111 | 111111111111 | 000000000000 |
| ABCDEF123456 | 123456ABCDEF | B9F9B9B9F9B9 |

## Error Handling

The generator includes comprehensive error handling:
- Invalid hexadecimal input validation
- Automatic padding for different length inputs
- Clear error messages for debugging

## Use Cases

This generator is ideal for:
- Cryptographic operations
- Data masking and encoding
- Scientific computing with hexadecimal data
- Reverse engineering applications
- Educational purposes for understanding XOR operations

## Requirements

- Python 3.6 or higher
- No external dependencies required

## License

This code is provided as-is for scientific and educational purposes.
