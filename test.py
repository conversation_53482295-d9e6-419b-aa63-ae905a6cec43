def hex_xor(hex1, hex2):
    # Pad the shorter string
    max_len = max(len(hex1), len(hex2))
    hex1 = hex1.zfill(max_len)
    hex2 = hex2.zfill(max_len)
    
    # XOR the numbers
    result = hex(int(hex1, 16) ^ int(hex2, 16))[2:].upper()
    
    # Pad to match output length (12 chars is common in your data)
    result = result.zfill(12)
    return result

# Test on known data
samples = [
    ('8636936827A', '2359D848460E'),
    ('4009671D41D', 'A5CCA550BA00'),
    ('4007C0600DA', 'A5CCA550BA00'),
    ('1111111111DA', 'A5CCA550BA00'),
    ('111111111111', '000000000000'),
    ('111111111111', '111111111111'),
    ('222222222222', '111111111111'),
    ('000000000000', '000000000000'),
]

for d1, d2 in samples:
    print(f'Data1: {d1}, Data2: {d2}, Output: {hex_xor(d1, d2)}')
