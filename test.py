class ScientificCalculator:
    """
    Number Input-Output Generator for Scientific Calculations
    Supports hexadecimal XOR operations with automatic formatting
    """

    def __init__(self, output_length=12):
        """
        Initialize the calculator

        Args:
            output_length (int): Desired length of output (default: 12 characters)
        """
        self.output_length = output_length

    def hex_xor(self, hex1, hex2):
        """
        Perform XOR operation on two hexadecimal numbers

        Args:
            hex1 (str): First hexadecimal number
            hex2 (str): Second hexadecimal number

        Returns:
            str: XOR result as uppercase hexadecimal string
        """
        # Remove any '0x' prefix and convert to uppercase
        hex1 = str(hex1).replace('0x', '').upper()
        hex2 = str(hex2).replace('0x', '').upper()

        # Pad the shorter string with leading zeros
        max_len = max(len(hex1), len(hex2))
        hex1 = hex1.zfill(max_len)
        hex2 = hex2.zfill(max_len)

        # Perform XOR operation
        result = hex(int(hex1, 16) ^ int(hex2, 16))[2:].upper()

        # Pad result to desired output length
        result = result.zfill(self.output_length)
        return result

    def calculate(self, data1, data2):
        """
        Main calculation method - currently implements XOR
        Can be extended for other scientific calculations

        Args:
            data1: First input value
            data2: Second input value

        Returns:
            str: Calculated output
        """
        return self.hex_xor(data1, data2)

    def batch_calculate(self, data_pairs):
        """
        Process multiple data pairs at once

        Args:
            data_pairs (list): List of tuples containing (data1, data2) pairs

        Returns:
            list: List of results
        """
        results = []
        for data1, data2 in data_pairs:
            output = self.calculate(data1, data2)
            results.append({
                'data1': data1,
                'data2': data2,
                'output': output
            })
        return results

    def validate_input(self, hex_string):
        """
        Validate if input is a valid hexadecimal string

        Args:
            hex_string (str): Input to validate

        Returns:
            bool: True if valid, False otherwise
        """
        try:
            hex_string = str(hex_string).replace('0x', '')
            int(hex_string, 16)
            return True
        except ValueError:
            return False

# Initialize calculator
calculator = ScientificCalculator()

# Test data samples
samples = [
    ('8636936827A', '2359D848460E'),
    ('4009671D41D', 'A5CCA550BA00'),
    ('4007C0600DA', 'A5CCA550BA00'),
    ('1111111111DA', 'A5CCA550BA00'),
    ('111111111111', '000000000000'),
    ('111111111111', '111111111111'),
    ('222222222222', '111111111111'),
    ('000000000000', '000000000000'),
]

print("=== Scientific Calculator - Hexadecimal XOR Generator ===")
print("Formula: Output = Data1 XOR Data2")
print("=" * 60)

# Process test samples
results = calculator.batch_calculate(samples)
for result in results:
    print(f"Data1: {result['data1']:>12} | Data2: {result['data2']:>12} | Output: {result['output']}")

print("\n" + "=" * 60)
print("Calculator ready for new inputs!")

def generate_output(data1, data2):
    """
    Convenience function to generate output for any new data pair

    Args:
        data1 (str): First hexadecimal input
        data2 (str): Second hexadecimal input

    Returns:
        str: Calculated output
    """
    if not calculator.validate_input(data1) or not calculator.validate_input(data2):
        return "ERROR: Invalid hexadecimal input"

    return calculator.calculate(data1, data2)

# Example usage for new data
print("\nExample usage for new data:")
print("generate_output('ABCDEF123456', '123456ABCDEF') =", generate_output('ABCDEF123456', '123456ABCDEF'))
