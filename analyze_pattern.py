#!/usr/bin/env python3
"""
Advanced pattern analysis to discover the real scientific calculation formula
"""

# Data from the screenshot - corrected and verified
screenshot_data = [
    ('8636936827A', '2359D848460E', '441719F73495'),
    ('4009671D41D', 'A5CCA550BA00', '685D67D8080C'),
    ('4007C0600DA', 'A5CCA550BA00', '4007C0600DA'),  # Returns Data1
    ('1111111111DA', 'A5CCA550BA00', '0F7651AD573F'),
    ('111111111111', 'A5CCA550BA00', '0F7651AD573F'),
    ('111111111111', '000000000000', '01754E9EDC43'),
    ('111111111111', '111111111111', '048778ED97F8'),
    ('000000000000', '111111111111', '02222222222E'),
    ('000000000000', '000000000000', '000000000000'),
]

def test_operations(data1, data2, expected):
    """Test various operations to find the pattern"""
    print(f"\nTesting: {data1} + {data2} = {expected}")

    # Convert to integers
    try:
        int1 = int(data1, 16)
        int2 = int(data2, 16)
        exp_int = int(expected, 16)

        # Test various operations
        operations = {
            'XOR': int1 ^ int2,
            'ADD': int1 + int2,
            'SUB (1-2)': int1 - int2 if int1 >= int2 else 0,
            'SUB (2-1)': int2 - int1 if int2 >= int1 else 0,
            'AND': int1 & int2,
            'OR': int1 | int2,
            'MUL': int1 * int2,
        }

        print(f"Expected: {expected}")
        for op_name, result in operations.items():
            hex_result = hex(result)[2:].upper().zfill(12)
            match = "✓" if hex_result == expected.upper().zfill(12) else "✗"
            print(f"{op_name:10}: {hex_result} {match}")

        # Check if it's just Data1
        data1_padded = data1.upper().zfill(12)
        if data1_padded == expected.upper().zfill(12):
            print(f"MATCH: Just Data1 padded!")

        # Check if it's just Data2
        data2_padded = data2.upper().zfill(12)
        if data2_padded == expected.upper().zfill(12):
            print(f"MATCH: Just Data2 padded!")

    except Exception as e:
        print(f"Error: {e}")

def analyze_bit_patterns():
    """Analyze bit-level patterns"""
    print("\n" + "=" * 60)
    print("BIT PATTERN ANALYSIS")
    print("=" * 60)

    for data1, data2, expected in screenshot_data:
        print(f"\nData1: {data1}")
        print(f"Data2: {data2}")
        print(f"Output: {expected}")

        # Convert to binary for bit analysis
        try:
            bin1 = bin(int(data1, 16))[2:].zfill(48)  # 12 hex = 48 bits
            bin2 = bin(int(data2, 16))[2:].zfill(48)
            bin_out = bin(int(expected, 16))[2:].zfill(48)

            print(f"Bin1:  {bin1}")
            print(f"Bin2:  {bin2}")
            print(f"BinOut:{bin_out}")

            # Check for patterns
            same_bits = sum(1 for i in range(48) if bin1[i] == bin2[i] == bin_out[i])
            print(f"Same bits in all three: {same_bits}/48")

        except Exception as e:
            print(f"Error in bit analysis: {e}")

        print("-" * 40)

def analyze_special_cases():
    """Look for special case patterns"""
    print("\n" + "=" * 60)
    print("SPECIAL CASE ANALYSIS")
    print("=" * 60)

    # Group by Data2 to see if there are patterns
    data2_groups = {}
    for data1, data2, expected in screenshot_data:
        if data2 not in data2_groups:
            data2_groups[data2] = []
        data2_groups[data2].append((data1, expected))

    for data2, pairs in data2_groups.items():
        print(f"\nWhen Data2 = {data2}:")
        for data1, expected in pairs:
            print(f"  {data1} -> {expected}")

            # Check if output relates to data1
            if data1.upper().zfill(12) == expected.upper():
                print(f"    *** OUTPUT = DATA1 (padded) ***")

            # Check for simple transformations
            try:
                data1_int = int(data1, 16)
                expected_int = int(expected, 16)
                diff = expected_int - data1_int
                ratio = expected_int / data1_int if data1_int != 0 else 0
                print(f"    Difference: {hex(diff) if diff >= 0 else '-' + hex(-diff)}")
                if ratio != 0:
                    print(f"    Ratio: {ratio:.6f}")
            except:
                pass
        print("-" * 30)

def analyze_checksums():
    """Test if this might be a checksum or hash function"""
    print("\n" + "=" * 60)
    print("CHECKSUM/HASH ANALYSIS")
    print("=" * 60)

    for data1, data2, expected in screenshot_data:
        print(f"\nInput: {data1} + {data2}")
        print(f"Output: {expected}")

        # Test various checksum approaches
        combined = data1 + data2
        print(f"Combined: {combined}")

        # Simple sum of hex digits
        digit_sum = sum(int(c, 16) for c in combined if c.isdigit() or c.upper() in 'ABCDEF')
        print(f"Digit sum: {digit_sum} (hex: {hex(digit_sum)})")

        # CRC-like patterns
        try:
            combined_int = int(combined, 16)
            # Test some polynomial divisions
            for poly in [0x1021, 0x8005, 0x04C11DB7]:  # Common CRC polynomials
                result = combined_int % poly
                result_hex = hex(result)[2:].upper().zfill(12)
                if result_hex == expected.upper():
                    print(f"*** MATCH: CRC with polynomial {hex(poly)} ***")
        except:
            pass

        print("-" * 30)

def analyze_position_dependency():
    """Check if output depends on position/order of inputs"""
    print("\n" + "=" * 60)
    print("POSITION DEPENDENCY ANALYSIS")
    print("=" * 60)

    # Look for cases where swapping data1/data2 might give insights
    for i, (data1, data2, expected) in enumerate(screenshot_data):
        # Check if there's a reverse case in the data
        for j, (data1_rev, data2_rev, expected_rev) in enumerate(screenshot_data):
            if i != j and data1 == data2_rev and data2 == data1_rev:
                print(f"Found swap pair:")
                print(f"  {data1} + {data2} = {expected}")
                print(f"  {data2} + {data1} = {expected_rev}")
                if expected == expected_rev:
                    print("  *** COMMUTATIVE OPERATION ***")
                else:
                    print("  *** NON-COMMUTATIVE OPERATION ***")
                print()

def analyze_all():
    """Comprehensive analysis"""
    print("=" * 60)
    print("COMPREHENSIVE PATTERN ANALYSIS")
    print("=" * 60)

    # Run all analysis functions
    for data1, data2, expected in screenshot_data:
        test_operations(data1, data2, expected)
        print("-" * 40)

    analyze_special_cases()
    analyze_bit_patterns()
    analyze_checksums()
    analyze_position_dependency()

if __name__ == "__main__":
    analyze_all()
