#!/usr/bin/env python3
"""
Analyze the pattern from the screenshot data to find the correct formula
"""

# Data from the screenshot
screenshot_data = [
    ('8636936827A', '2359D848460E', '441719F73495'),
    ('4009671D41D', 'A5CCA550BA00', '685D67D8080C'),
    ('4007C0600DA', 'A5CCA550BA00', '4007C0600DA'),  # This one seems to just return Data1
    ('1111111111DA', 'A5CCA550BA00', 'F7651AD573F'),
    ('111111111111', 'A5CCA550BA00', 'F7651AD573F'),
    ('111111111111', '000000000000', '1754E9EDC43'),
    ('111111111111', '111111111111', '48778ED97F8'),
    ('000000000000', '111111111111', '2222222222E'),
    ('000000000000', '000000000000', '000000000000'),
]

def test_operations(data1, data2, expected):
    """Test various operations to find the pattern"""
    print(f"\nTesting: {data1} + {data2} = {expected}")
    
    # Convert to integers
    try:
        int1 = int(data1, 16)
        int2 = int(data2, 16)
        exp_int = int(expected, 16)
        
        # Test various operations
        operations = {
            'XOR': int1 ^ int2,
            'ADD': int1 + int2,
            'SUB (1-2)': int1 - int2 if int1 >= int2 else 0,
            'SUB (2-1)': int2 - int1 if int2 >= int1 else 0,
            'AND': int1 & int2,
            'OR': int1 | int2,
            'MUL': int1 * int2,
        }
        
        print(f"Expected: {expected}")
        for op_name, result in operations.items():
            hex_result = hex(result)[2:].upper().zfill(12)
            match = "✓" if hex_result == expected.upper().zfill(12) else "✗"
            print(f"{op_name:10}: {hex_result} {match}")
            
        # Check if it's just Data1
        data1_padded = data1.upper().zfill(12)
        if data1_padded == expected.upper().zfill(12):
            print(f"MATCH: Just Data1 padded!")
            
        # Check if it's just Data2  
        data2_padded = data2.upper().zfill(12)
        if data2_padded == expected.upper().zfill(12):
            print(f"MATCH: Just Data2 padded!")
            
    except Exception as e:
        print(f"Error: {e}")

def analyze_all():
    """Analyze all data points"""
    print("=" * 60)
    print("PATTERN ANALYSIS FROM SCREENSHOT DATA")
    print("=" * 60)
    
    for data1, data2, expected in screenshot_data:
        test_operations(data1, data2, expected)
        print("-" * 40)

if __name__ == "__main__":
    analyze_all()
