#!/usr/bin/env python3
"""
Pattern Discovery - Based on analysis findings
Key discoveries:
1. Operation is NON-COMMUTATIVE (order matters)
2. Special case: when Data2 = A5CCA550BA00 and Data1 = 4007C0600DA, output = Data1
3. The algorithm appears to be some form of transformation/encoding
"""

# Corrected data from screenshot
data_pairs = [
    ('8636936827A', '2359D848460E', '441719F73495'),
    ('4009671D41D', 'A5CCA550BA00', '685D67D8080C'),
    ('4007C0600DA', 'A5CCA550BA00', '4007C0600DA'),  # Special case: output = data1
    ('1111111111DA', 'A5CCA550BA00', '0F7651AD573F'),
    ('111111111111', 'A5CCA550BA00', '0F7651AD573F'),
    ('111111111111', '000000000000', '01754E9EDC43'),
    ('111111111111', '111111111111', '048778ED97F8'),
    ('000000000000', '111111111111', '02222222222E'),
    ('000000000000', '000000000000', '000000000000'),
]

def test_hypothesis_1():
    """
    Hypothesis 1: It might be a conditional operation
    If Data1 == Data2, return some transformation
    If Data2 == specific value, return Data1
    """
    print("=" * 60)
    print("HYPOTHESIS 1: CONDITIONAL OPERATIONS")
    print("=" * 60)
    
    for data1, data2, expected in data_pairs:
        print(f"\nData1: {data1}")
        print(f"Data2: {data2}")
        print(f"Expected: {expected}")
        
        # Check special conditions
        if data1 == data2:
            print("  -> Data1 == Data2")
        
        if data2 == 'A5CCA550BA00':
            print("  -> Data2 is the special value A5CCA550BA00")
            if expected.upper() == data1.upper().zfill(12):
                print("  -> OUTPUT = DATA1 (confirmed)")
        
        if data2 == '000000000000':
            print("  -> Data2 is zero")
        
        if data1 == '000000000000':
            print("  -> Data1 is zero")

def test_hypothesis_2():
    """
    Hypothesis 2: It might be a lookup table or hash-based system
    """
    print("\n" + "=" * 60)
    print("HYPOTHESIS 2: HASH/LOOKUP PATTERNS")
    print("=" * 60)
    
    # Test if there's a pattern in the first few digits
    for data1, data2, expected in data_pairs:
        print(f"\n{data1} + {data2} = {expected}")
        
        # Check if first digits have patterns
        first_d1 = data1[0] if data1 else '0'
        first_d2 = data2[0] if data2 else '0'
        first_out = expected[0] if expected else '0'
        
        print(f"  First digits: {first_d1} + {first_d2} = {first_out}")
        
        # Check last digits
        last_d1 = data1[-1] if data1 else '0'
        last_d2 = data2[-1] if data2 else '0'
        last_out = expected[-1] if expected else '0'
        
        print(f"  Last digits:  {last_d1} + {last_d2} = {last_out}")

def test_hypothesis_3():
    """
    Hypothesis 3: Bit manipulation with specific rules
    """
    print("\n" + "=" * 60)
    print("HYPOTHESIS 3: BIT MANIPULATION RULES")
    print("=" * 60)
    
    for data1, data2, expected in data_pairs:
        try:
            # Convert to integers
            int1 = int(data1, 16)
            int2 = int(data2, 16)
            exp_int = int(expected, 16)
            
            print(f"\n{data1} + {data2} = {expected}")
            
            # Test bit shifts and combinations
            shift_tests = [
                ("Data1 << 1", int1 << 1),
                ("Data1 >> 1", int1 >> 1),
                ("Data2 << 1", int2 << 1),
                ("Data2 >> 1", int2 >> 1),
                ("(Data1 + Data2) >> 1", (int1 + int2) >> 1),
                ("(Data1 ^ Data2) + Data1", (int1 ^ int2) + int1),
                ("Data1 & 0xFFFFFFFFFFFF", int1 & 0xFFFFFFFFFFFF),
            ]
            
            for test_name, result in shift_tests:
                result_hex = hex(result)[2:].upper().zfill(12)
                if result_hex == expected.upper().zfill(12):
                    print(f"  *** MATCH: {test_name} ***")
                    
        except Exception as e:
            print(f"  Error: {e}")

def test_hypothesis_4():
    """
    Hypothesis 4: Algorithm with specific constants or magic numbers
    """
    print("\n" + "=" * 60)
    print("HYPOTHESIS 4: MAGIC NUMBER PATTERNS")
    print("=" * 60)
    
    # Look for patterns in the differences
    magic_candidates = []
    
    for data1, data2, expected in data_pairs:
        try:
            int1 = int(data1, 16)
            int2 = int(data2, 16)
            exp_int = int(expected, 16)
            
            # Calculate various combinations that might reveal magic numbers
            combinations = [
                exp_int - int1,
                exp_int - int2,
                exp_int - (int1 + int2),
                exp_int - (int1 ^ int2),
                exp_int - (int1 & int2),
                exp_int - (int1 | int2),
            ]
            
            print(f"\n{data1} + {data2} = {expected}")
            for i, combo in enumerate(combinations):
                if combo != 0:
                    hex_combo = hex(combo)[2:].upper() if combo > 0 else '-' + hex(-combo)[2:].upper()
                    print(f"  Magic candidate {i}: {hex_combo}")
                    magic_candidates.append(combo)
                    
        except Exception as e:
            print(f"  Error: {e}")
    
    # Look for repeated magic numbers
    from collections import Counter
    magic_counts = Counter(magic_candidates)
    print(f"\nMost common magic numbers:")
    for magic, count in magic_counts.most_common(5):
        if count > 1:
            hex_magic = hex(magic)[2:].upper() if magic > 0 else '-' + hex(-magic)[2:].upper()
            print(f"  {hex_magic}: appears {count} times")

def test_hypothesis_5():
    """
    Hypothesis 5: It's a cryptographic or encoding function
    """
    print("\n" + "=" * 60)
    print("HYPOTHESIS 5: CRYPTOGRAPHIC PATTERNS")
    print("=" * 60)
    
    for data1, data2, expected in data_pairs:
        # Test if it might be related to common crypto operations
        combined = data1 + data2
        print(f"\nInput: {data1} + {data2}")
        print(f"Combined: {combined}")
        print(f"Expected: {expected}")
        
        # Test simple transformations of combined input
        try:
            combined_int = int(combined, 16)
            
            # Test modular arithmetic with common moduli
            moduli = [2**32 - 1, 2**48 - 1, 2**64 - 1, 0xFFFFFFFFFFF]
            for mod in moduli:
                result = combined_int % mod
                result_hex = hex(result)[2:].upper().zfill(12)
                if result_hex == expected.upper().zfill(12):
                    print(f"  *** MATCH: Combined % {hex(mod)} ***")
                    
        except Exception as e:
            print(f"  Error: {e}")

if __name__ == "__main__":
    test_hypothesis_1()
    test_hypothesis_2()
    test_hypothesis_3()
    test_hypothesis_4()
    test_hypothesis_5()
