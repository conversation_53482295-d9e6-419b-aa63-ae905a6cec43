#!/usr/bin/env python3
"""
Formula Discovery - Based on key patterns found:

KEY FINDINGS:
1. When Data2 = A5CCA550BA00 and Data1 = 4007C0600DA: Output = Data1 (special case)
2. Magic numbers appear repeatedly: -F9BC27234CE, -C8998237919, -EEEEEEEEEE3
3. First/last digit patterns show some correlation
4. One case matched "Data1 & 0xFFFFFFFFFFFF"

Let me test a hypothesis: This might be a conditional algorithm with specific rules
"""

data_pairs = [
    ('8636936827A', '2359D848460E', '441719F73495'),
    ('4009671D41D', 'A5CCA550BA00', '685D67D8080C'),
    ('4007C0600DA', 'A5CCA550BA00', '4007C0600DA'),  # Special case
    ('1111111111DA', 'A5CCA550BA00', '0F7651AD573F'),
    ('111111111111', 'A5CCA550BA00', '0F7651AD573F'),
    ('111111111111', '000000000000', '01754E9EDC43'),
    ('111111111111', '111111111111', '048778ED97F8'),
    ('000000000000', '111111111111', '02222222222E'),
    ('000000000000', '000000000000', '000000000000'),
]

def test_conditional_algorithm():
    """
    Test if this is a conditional algorithm with specific rules
    """
    print("=" * 70)
    print("TESTING CONDITIONAL ALGORITHM HYPOTHESIS")
    print("=" * 70)
    
    def proposed_algorithm(data1_hex, data2_hex):
        """
        Proposed algorithm based on observed patterns
        """
        data1 = data1_hex.upper().zfill(12)
        data2 = data2_hex.upper().zfill(12)
        
        # Rule 1: If both are zero, return zero
        if data1 == '000000000000' and data2 == '000000000000':
            return '000000000000'
        
        # Rule 2: Special case for specific Data2 value
        if data2 == 'A5CCA550BA00':
            if data1 == '4007C0600DA':
                return data1  # Return Data1 as-is
            else:
                # Some transformation for other Data1 values with this Data2
                # This needs to be discovered
                pass
        
        # Rule 3: When Data1 == Data2
        if data1 == data2:
            if data1 == '111111111111':
                return '048778ED97F8'
            # Other cases need discovery
        
        # Rule 4: When one is zero
        if data1 == '000000000000':
            if data2 == '111111111111':
                return '02222222222E'
        
        if data2 == '000000000000':
            if data1 == '111111111111':
                return '01754E9EDC43'
        
        # Default: unknown pattern
        return "UNKNOWN"
    
    print("Testing proposed algorithm:")
    for data1, data2, expected in data_pairs:
        result = proposed_algorithm(data1, data2)
        match = "✓" if result == expected.upper().zfill(12) else "✗"
        print(f"{data1} + {data2} = {expected} | Predicted: {result} {match}")

def analyze_magic_number_pattern():
    """
    Analyze the magic numbers to see if they form a pattern
    """
    print("\n" + "=" * 70)
    print("MAGIC NUMBER ANALYSIS")
    print("=" * 70)
    
    # The repeated magic numbers
    magic_numbers = [
        0xF9BC27234CE,   # Appears 4 times
        0xC8998237919,   # Appears 4 times  
        0xEEEEEEEEEE3,   # Appears 4 times
    ]
    
    print("Analyzing magic numbers:")
    for magic in magic_numbers:
        print(f"\nMagic number: {hex(magic)}")
        print(f"Binary: {bin(magic)}")
        print(f"Decimal: {magic}")
        
        # Check if it's related to common constants
        if magic == 0xEEEEEEEEEE3:
            print("  -> This looks like a bit pattern (mostly E's)")
        
        # Check relationships between magic numbers
        for other_magic in magic_numbers:
            if magic != other_magic:
                diff = magic - other_magic
                print(f"  Difference with {hex(other_magic)}: {hex(diff)}")

def test_transformation_hypothesis():
    """
    Test if there's a consistent transformation being applied
    """
    print("\n" + "=" * 70)
    print("TRANSFORMATION HYPOTHESIS")
    print("=" * 70)
    
    # Focus on cases with same Data2 to find pattern
    a5cc_cases = [
        ('4009671D41D', 'A5CCA550BA00', '685D67D8080C'),
        ('4007C0600DA', 'A5CCA550BA00', '4007C0600DA'),
        ('1111111111DA', 'A5CCA550BA00', '0F7651AD573F'),
        ('111111111111', 'A5CCA550BA00', '0F7651AD573F'),
    ]
    
    print("Cases with Data2 = A5CCA550BA00:")
    for data1, data2, expected in a5cc_cases:
        print(f"\n{data1} -> {expected}")
        
        try:
            data1_int = int(data1, 16)
            expected_int = int(expected, 16)
            
            # Test various transformations
            transformations = [
                ("Identity", data1_int),
                ("Complement", (~data1_int) & 0xFFFFFFFFFFFF),
                ("Rotate left 4", ((data1_int << 4) | (data1_int >> 44)) & 0xFFFFFFFFFFFF),
                ("Rotate right 4", ((data1_int >> 4) | (data1_int << 44)) & 0xFFFFFFFFFFFF),
                ("XOR with magic", data1_int ^ 0xA5CCA550BA00),
                ("Add magic mod 2^48", (data1_int + 0xA5CCA550BA00) & 0xFFFFFFFFFFFF),
                ("Subtract magic", (data1_int - 0xA5CCA550BA00) & 0xFFFFFFFFFFFF),
            ]
            
            for name, result in transformations:
                result_hex = hex(result)[2:].upper().zfill(12)
                if result_hex == expected.upper().zfill(12):
                    print(f"  *** MATCH: {name} ***")
                    
        except Exception as e:
            print(f"  Error: {e}")

def test_lookup_table_hypothesis():
    """
    Test if this might be a lookup table or hash function
    """
    print("\n" + "=" * 70)
    print("LOOKUP TABLE HYPOTHESIS")
    print("=" * 70)
    
    # Create a simple lookup table from known values
    lookup_table = {}
    for data1, data2, expected in data_pairs:
        key = data1.upper() + "|" + data2.upper()
        lookup_table[key] = expected.upper()
    
    print("Known lookup table entries:")
    for key, value in lookup_table.items():
        print(f"  {key} -> {value}")
    
    # Test if we can predict unknown values
    test_cases = [
        ('ABCD1234', '2359D848460E'),
        ('4007C0600DA', '000000000000'),
        ('8636936827A', 'A5CCA550BA00'),
    ]
    
    print(f"\nTesting unknown cases:")
    for data1, data2 in test_cases:
        key = data1.upper() + "|" + data2.upper()
        if key in lookup_table:
            print(f"  {data1} + {data2} = {lookup_table[key]} (known)")
        else:
            print(f"  {data1} + {data2} = UNKNOWN (need algorithm)")

if __name__ == "__main__":
    test_conditional_algorithm()
    analyze_magic_number_pattern()
    test_transformation_hypothesis()
    test_lookup_table_hypothesis()
